package main

import (
	"os"
	"payment-sdk/payment"
	paymentstripe "payment-sdk/payment-stripe"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func main() {
	gcfg := &payment.GatewayConfig{}

	encoder := zapcore.NewJSONEncoder(zap.NewProductionEncoderConfig())
	writeSyncer := zapcore.AddSync(os.Stdout)
	level := zap.NewAtomicLevelAt(zapcore.ErrorLevel)
	core := zapcore.NewCore(encoder, writeSyncer, level)
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	paymentstripe.NewPaymentGatewayStripe(gcfg, logger)
}
