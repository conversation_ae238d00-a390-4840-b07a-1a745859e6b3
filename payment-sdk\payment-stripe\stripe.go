package paymentstripe

import (
	"fmt"
	"net/http"
	"payment-sdk/payment"

	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/checkout/session"
	"github.com/stripe/stripe-go/webhook"
	"go.uber.org/zap"
)

const (
	PROVIDER_STRIPE = "stripe"
)

// PaymentGateway 支付网关接口
type PaymentGatewayStrip struct {
	Logger *zap.Logger
}

// NewPaymentGatewayStripe 创建支付网关
func NewPaymentGatewayStripe(gcfg *payment.GatewayConfig, logger *zap.Logger) *PaymentGatewayStrip {
	stripe.Key = gcfg.SecretKey
	return &PaymentGatewayStrip{
		Logger: logger,
	}
}

// CreateCheckoutSession 创建结账会话
func (g *PaymentGatewayStrip) CreateCheckout(req *payment.CheckoutReq) (string, error) {

	g.Logger.Info("Creating mock checkout session",
		zap.String("order_id", req.OrderID),
	)

	// TODO: 塞入 OrderID

	// Create new Checkout Session for the order
	// Other optional params include:
	// [billing_address_collection] - to display billing address details on the page
	// [customer] - if you have an existing Stripe Customer ID
	// [payment_intent_data] - lets capture the payment later
	// [customer_email] - lets you prefill the email input in the form
	// [automatic_tax] - to automatically calculate sales tax, VAT and GST in the checkout page
	// For full details see https://stripe.com/docs/api/checkout/sessions/create

	// ?session_id={CHECKOUT_SESSION_ID} means the redirect will have the session ID
	// set as a query param
	params := &stripe.CheckoutSessionParams{
		ClientReferenceID: stripe.String(req.OrderID),
		SuccessURL:        stripe.String(req.SuccessURL),
		CancelURL:         stripe.String(req.CancelURL),
		Mode:              stripe.String(string(stripe.CheckoutSessionModePayment)),
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				Quantity: stripe.Int64(int64(req.Quantity)),
				Price:    stripe.String(req.PriceID),
			},
		},
		// AutomaticTax: &stripe.CheckoutSessionAutomaticTaxParams{Enabled: stripe.Bool(true)},

		// 保存支付方式，以在会话外扣款
		// https://docs.stripe.com/payments/accept-a-payment?platform=web&ui=stripe-hosted#save-payment-methods-to-charge-them-off-session
		PaymentIntentData: &stripe.CheckoutSessionPaymentIntentDataParams{
			SetupFutureUsage: stripe.String("off_session"),
		},
	}
	s, err := session.New(params)
	if err != nil {
		return "", fmt.Errorf("error while creating session %v", err.Error())
	}

	return s.URL, nil
}

// GetPaymentStatus 获取支付状态
func (g *PaymentGatewayStrip) GetPaymentStatus(pspPaymentID string) (string, error) {
	g.Logger.Info("Getting mock payment status",
		zap.String("psp_payment_id", pspPaymentID),
	)

	// 模拟返回支付状态
	// 在真实实现中，这里会调用支付提供商的API
	return "", nil
}

// RefundPayment 退款
func (g *PaymentGatewayStrip) RefundPayment(pspPaymentID string, amount float64) error {
	g.Logger.Info("Processing mock refund",
		zap.String("psp_payment_id", pspPaymentID),
		zap.Float64("amount", amount),
	)

	// 模拟退款处理
	// 在真实实现中，这里会调用支付提供商的退款API
	return nil
}

// Webhook 支付网关事件回调
func (g *PaymentGatewayStrip) Webhook(header http.Header, payload []byte, cb func(int, string)) error {
	g.Logger.Info("Verifying Webhook",
		zap.String("header", header.Get("Stripe-Signature")),
		zap.Int("payload_size", len(payload)),
	)

	event, err := webhook.ConstructEvent(payload, header.Get("Stripe-Signature"), stripe.Key)
	if err != nil {
		g.Logger.Warn("webhook.ConstructEvent", zap.Error(err))
		return err
	}

	session := event.Data.Object
	clientReferenceId, exist := session["client_reference_id"]
	if !exist {
		err := fmt.Errorf("not found client_reference_id in session:%+v", session)
		g.Logger.Warn("Webhook", zap.Error(err))
		return err
	}
	g.Logger.Info("Webhook", zap.String("clientReferenceId", clientReferenceId.(string)))

	if event.Type == "checkout.session.completed" {
		// 订阅创建成功，可以激活用户权限
		fmt.Println("Checkout Session completed!")

	} else if event.Type == "checkout.session.expired" {
		fmt.Println("checkout.session.expired!")

	} else if event.Type == "checkout.session.async_payment_succeeded" {
		fmt.Println("checkout.session.async_payment_succeeded")

	} else if event.Type == "checkout.session.async_payment_failed" {
		fmt.Println("checkout.session.async_payment_failed")

	} else if event.Type == "invoice.payment_succeeded" {
		// 每月成功续费，可以延长会员时间
	} else if event.Type == "invoice.payment_failed" {
		// 自动续费失败，提醒用户处理付款方式
	} else if event.Type == "invoice.payment_failed" {
		// 自动续费失败，提醒用户处理付款方式
	}

	return nil
}
