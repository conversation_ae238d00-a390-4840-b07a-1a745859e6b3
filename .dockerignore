# Git 相关
.git
.gitignore
.gitattributes

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
Thumbs.db

# 构建产物和缓存
payment-backend/payment-backend
payment-backend/payment-backend.exe
payment-sdk/payment-sdk.exe
*/build/
*/dist/
*/target/

# 测试和覆盖率文件
payment-backend/coverage.out
payment-backend/coverage.html
payment-backend/coverage/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 文档（可选，如果不需要在容器中）
*.md
payment-backend/docs/
payment-backend/examples/

# 脚本文件（构建时不需要）
payment-backend/scripts/

# 开发配置文件（如果有敏感信息）
.env
.env.local
.env.development
.env.test

# Node.js 相关（如果有前端代码）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 其他不需要的目录
payment-common/
payment-sync/
