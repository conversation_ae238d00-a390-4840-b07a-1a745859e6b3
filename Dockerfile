# 第一阶段：构建阶段
FROM harbor.risc-verse.cn/library/golang:1.24.4 AS builder

# 设置工作目录
WORKDIR /app

# 复制 go.mod 和 go.sum 文件
COPY go.mod go.sum ./

# 设置 Go 代理并下载依赖
RUN export GO111MODULE=on && \
    export GOPROXY=https://nexus-console.risc-verse.cn/repository/goproxy,direct && \
    go mod download

# 复制源代码
COPY payment-backend .
RUN cd payment-backend

# 构建应用程序
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags "-s -w -X 'main.VersionNumber=1.0.0'" \
    -o payment-backend .

# 第二阶段：运行阶段
FROM harbor.risc-verse.cn/library/alpine:3.20-cn

# 安装必要的包（如果需要）
RUN apk --no-cache add ca-certificates tzdata

# 创建非 root 用户
RUN adduser -D -g '' appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/payment-backend /app/

# 复制配置文件
COPY --from=builder /app/configs /app/configs

# 设置文件权限
RUN chown -R appuser:appuser /app

# 切换到非 root 用户
USER appuser

# 暴露端口（根据你的应用配置调整）
EXPOSE 8080

# # 设置健康检查（可选）
# HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
#     CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动应用程序
ENTRYPOINT ["./payment-backend"]
CMD ["serve"]
