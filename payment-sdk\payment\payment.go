package payment

import "net/http"

// Order 订单实体
type CheckoutReq struct {
	OrderID    string `json:"order_id"`
	UserID     string `json:"user_id"`
	ProductID  string `json:"product_id"`
	PriceID    string `json:"price_id"`
	Quantity   uint32 `json:"quantity"`
	SuccessURL string `json:"success_url"`
	CancelURL  string `json:"cancel_url"`
}

type GatewayConfig struct {
	SecretKey string `json:"secret_key"`
}

// PaymentGateway 支付网关接口
type PaymentGateway interface {
	CreateCheckout(req *CheckoutReq) (string, error) // 返回checkout URL
	GetPaymentStatus(pspPaymentID string) (string, error)
	RefundPayment(pspPaymentID string, amount float64) error
	Webhook(header http.Header, payload []byte, cb func(int, string)) error
}
